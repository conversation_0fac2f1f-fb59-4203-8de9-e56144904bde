#!/usr/bin/env python3
"""
混合精度训练测试脚本
"""

import subprocess
import time
import sys
import threading

def monitor_gpu_memory():
    """监控GPU内存使用"""
    print("开始监控GPU内存...")
    
    for i in range(60):  # 监控60秒
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,memory.used,memory.total,power.draw',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=3)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for gpu_id, line in enumerate(lines):
                    if line.strip():
                        util, mem_used, mem_total, power = line.split(', ')
                        if int(util) > 0:
                            mem_percent = int(mem_used) / int(mem_total) * 100
                            print(f"GPU {gpu_id}: {util}% 利用率, {mem_used}MB/{mem_total}MB ({mem_percent:.1f}%) 内存, {power}W 功耗")
        except:
            pass
        
        time.sleep(1)

def test_precision_mode(mode, batch_size=16):
    """测试不同精度模式"""
    print(f"\n=== 测试 {mode} 模式 (batch_size={batch_size}) ===")
    
    if mode == "fp32":
        cmd = [sys.executable, "train_tb.py", "--batch_size", str(batch_size)]
    elif mode == "fp16":
        cmd = [sys.executable, "train_tb.py", "--batch_size", str(batch_size), "--use_fp16"]
    elif mode == "amp":
        cmd = [sys.executable, "train_tb.py", "--batch_size", str(batch_size), "--use_amp"]
    else:
        print(f"未知模式: {mode}")
        return None
    
    start_time = time.time()
    batch_times = []
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            cwd="DCL"
        )
        
        batch_count = 0
        for line in process.stdout:
            print(line.strip())
            
            # 检测batch完成
            if "Step" in line and "Multi-verb" in line:
                batch_count += 1
                if batch_count >= 5:  # 只测试5个batch
                    process.terminate()
                    break
            
            # 检测batch时间
            if "Avg batch time:" in line:
                try:
                    time_str = line.split("Avg batch time: ")[1].split("s")[0]
                    batch_time = float(time_str)
                    batch_times.append(batch_time)
                except:
                    pass
        
        process.wait()
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_batch_time = sum(batch_times) / len(batch_times) if batch_times else total_time / max(batch_count, 1)
        
        result = {
            'mode': mode,
            'batch_size': batch_size,
            'total_time': total_time,
            'batch_count': batch_count,
            'avg_batch_time': avg_batch_time,
            'estimated_hours': avg_batch_time * 5924 / 3600
        }
        
        print(f"\n{mode} 结果:")
        print(f"  总时间: {total_time:.1f}s")
        print(f"  处理batch数: {batch_count}")
        print(f"  平均每batch: {avg_batch_time:.2f}s")
        print(f"  预计完整训练时间: {result['estimated_hours']:.1f}小时")
        
        return result
        
    except Exception as e:
        print(f"{mode} 测试失败: {e}")
        return None

def main():
    print("=== 混合精度训练速度对比测试 ===")
    print("将测试FP32、FP16和AMP三种模式的训练速度")
    print("每种模式只运行5个batch进行对比\n")
    
    # 启动GPU监控
    monitor_thread = threading.Thread(target=monitor_gpu_memory)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # 测试不同精度模式
    modes = ["fp32", "amp", "fp16"]  # 按推荐顺序测试
    results = []
    
    for mode in modes:
        try:
            result = test_precision_mode(mode, batch_size=16)
            if result:
                results.append(result)
            
            print(f"\n等待5秒后测试下一个模式...")
            time.sleep(5)
            
        except KeyboardInterrupt:
            print(f"\n用户中断测试")
            break
        except Exception as e:
            print(f"{mode} 模式测试出错: {e}")
    
    # 输出对比结果
    print("\n" + "="*60)
    print("=== 精度模式对比总结 ===")
    print("="*60)
    
    if results:
        print(f"{'模式':<8} {'平均时间/batch':<15} {'预计总时间':<12} {'相对速度':<10}")
        print("-" * 60)
        
        baseline_time = None
        for result in results:
            if baseline_time is None:
                baseline_time = result['avg_batch_time']
            
            speedup = baseline_time / result['avg_batch_time']
            print(f"{result['mode']:<8} {result['avg_batch_time']:.2f}s{'':<10} {result['estimated_hours']:.1f}h{'':<7} {speedup:.2f}x")
        
        # 推荐最佳模式
        best_result = min(results, key=lambda x: x['avg_batch_time'])
        print(f"\n🚀 推荐使用: {best_result['mode']} 模式")
        print(f"预计训练时间: {best_result['estimated_hours']:.1f}小时")
        
        # 给出具体命令
        if best_result['mode'] == 'fp32':
            cmd = f"python train_tb.py --batch_size {best_result['batch_size']}"
        elif best_result['mode'] == 'fp16':
            cmd = f"python train_tb.py --batch_size {best_result['batch_size']} --use_fp16"
        elif best_result['mode'] == 'amp':
            cmd = f"python train_tb.py --batch_size {best_result['batch_size']} --use_amp"
        
        print(f"运行命令: cd DCL && {cmd}")
        
    else:
        print("❌ 所有测试都失败了，请检查环境配置")

if __name__ == "__main__":
    main()
