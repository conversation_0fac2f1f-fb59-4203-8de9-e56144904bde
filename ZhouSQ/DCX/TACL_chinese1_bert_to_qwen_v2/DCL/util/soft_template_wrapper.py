#!/usr/bin/env python3
"""
为软提示模板创建兼容的tokenizer wrapper
"""

import torch
from typing import *
from transformers.tokenization_utils import PreTrainedTokenizer
from openprompt.plms.lm import LMTokenizerWrapper
from openprompt.plms.mlm import MLMTokenizerWrapper
from openprompt.data_utils import InputExample, InputFeatures


class SoftTemplateTokenizerWrapper(LMTokenizerWrapper):
    """
    为软提示模板创建的tokenizer wrapper
    兼容SimplifiedSoftTemplate
    """
    
    def __init__(self, 
                 tokenizer: PreTrainedTokenizer,
                 max_seq_length: int = 512,
                 decoder_max_length: int = 3,
                 truncate_method: str = "tail",
                 predict_eos_token: bool = False,
                 **kwargs):
        super().__init__(tokenizer=tokenizer,
                         max_seq_length=max_seq_length, 
                         decoder_max_length=decoder_max_length,
                         truncate_method=truncate_method,
                         predict_eos_token=predict_eos_token,
                         **kwargs)
    
    def tokenize_one_example(self, wrapped_example, teacher_forcing):
        """
        处理软提示模板包装后的样本
        
        Args:
            wrapped_example: 可能是元组(example, others)或InputExample对象
            teacher_forcing: 是否使用teacher forcing
            
        Returns:
            dict: 包含tokenized结果的字典
        """
        # 处理wrapped_example可能是元组或单个对象的情况
        if isinstance(wrapped_example, tuple):
            example, others = wrapped_example
        else:
            example = wrapped_example
            others = {}
        
        # 获取文本
        if isinstance(example, InputExample):
            text = example.text_a
            label = example.label
        else:
            text = example.get('text_a', '')
            label = example.get('label', 0)
        
        # 编码文本
        encoding = self.tokenizer(text,
                                 return_tensors="pt",
                                 padding="max_length",
                                 truncation=True,
                                 max_length=self.max_seq_length)
        
        input_ids = encoding["input_ids"].squeeze(0)
        attention_mask = encoding["attention_mask"].squeeze(0)
        
        # 创建loss_ids (用于指示哪些位置计算loss)
        # 对于软提示模板，我们只在原始文本部分计算loss
        loss_ids = torch.zeros_like(input_ids)
        
        # 创建decoder输入
        decoder_input_ids = torch.tensor([self.tokenizer.pad_token_id if self.tokenizer.pad_token_id is not None else 0])
        
        # 处理标签
        if teacher_forcing:
            # 使用标签作为decoder输入
            label_text = self.tokenizer.decode(label) if isinstance(label, torch.Tensor) else str(label)
            decoder_inputs = self.tokenizer(label_text, 
                                          return_tensors="pt",
                                          padding="max_length",
                                          truncation=True,
                                          max_length=self.decoder_max_length)
            decoder_input_ids = decoder_inputs["input_ids"].squeeze(0)
        
        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "loss_ids": loss_ids,
            "decoder_input_ids": decoder_input_ids,
            "label": label
        }


class SoftTemplateMLMTokenizerWrapper(MLMTokenizerWrapper):
    """
    为软提示模板创建的MLM tokenizer wrapper
    兼容SimplifiedSoftTemplate
    """
    
    def __init__(self, 
                 tokenizer: PreTrainedTokenizer,
                 mask_token: str = "[MASK]",
                 max_seq_length: int = 512,
                 decoder_max_length: int = 3,
                 truncate_method: str = "tail",
                 **kwargs):
        super().__init__(tokenizer=tokenizer,
                         mask_token=mask_token,
                         max_seq_length=max_seq_length, 
                         truncate_method=truncate_method,
                         **kwargs)
        self.decoder_max_length = decoder_max_length
    
    def tokenize_one_example(self, wrapped_example, teacher_forcing):
        """
        处理软提示模板包装后的样本
        
        Args:
            wrapped_example: 可能是元组(example, others)或InputExample对象
            teacher_forcing: 是否使用teacher forcing
            
        Returns:
            dict: 包含tokenized结果的字典
        """
        # 处理wrapped_example可能是元组或单个对象的情况
        if isinstance(wrapped_example, tuple):
            example, others = wrapped_example
        else:
            example = wrapped_example
            others = {}
        
        # 获取文本
        if isinstance(example, InputExample):
            text = example.text_a
            label = example.label
        else:
            text = example.get('text_a', '')
            label = example.get('label', 0)
        
        # 编码文本
        encoding = self.tokenizer(text,
                                 return_tensors="pt",
                                 padding="max_length",
                                 truncation=True,
                                 max_length=self.max_seq_length)
        
        input_ids = encoding["input_ids"].squeeze(0)
        attention_mask = encoding["attention_mask"].squeeze(0)
        
        # 查找mask token位置
        mask_token_id = self.tokenizer.mask_token_id
        mask_positions = (input_ids == mask_token_id).nonzero(as_tuple=True)[0]
        
        # 创建loss_ids (用于指示哪些位置计算loss)
        loss_ids = torch.zeros_like(input_ids)
        if len(mask_positions) > 0:
            loss_ids[mask_positions] = 1
        
        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "loss_ids": loss_ids,
            "label": label
        }


def get_soft_template_wrapper(model_type):
    """
    根据模型类型获取合适的wrapper类
    
    Args:
        model_type: 模型类型，如'bert', 'gpt2', 'qwen', 't5'等
        
    Returns:
        适合该模型的wrapper类
    """
    if model_type.lower() in ['bert', 'roberta', 'albert', 'ernie']:
        return SoftTemplateMLMTokenizerWrapper
    elif model_type.lower() in ['gpt2', 'gpt', 'qwen', 'llama', 'baichuan']:
        return SoftTemplateTokenizerWrapper
    else:
        # 默认使用LM wrapper
        return SoftTemplateTokenizerWrapper
