#!/usr/bin/env python3
"""
实现论文中的相似度计算公式
论文公式(5): sim(x,x') = Σ(2^(j-1)/(2^C-1)) * cos(m_j, m_j')
"""

import torch
import torch.nn.functional as F
import numpy as np


def hierarchical_similarity(index_vectors_1, index_vectors_2, num_layers):
    """
    计算论文公式(5)中的层次化加权相似度
    
    Args:
        index_vectors_1: 第一个样本的索引向量 [num_layers, hidden_size]
        index_vectors_2: 第二个样本的索引向量 [num_layers, hidden_size]  
        num_layers: 层次数量 C
        
    Returns:
        torch.Tensor: 加权相似度分数
    """
    total_similarity = 0.0
    normalization_factor = 2**num_layers - 1
    
    for j in range(num_layers):
        # 计算第j层的权重: 2^(j-1) / (2^C - 1)
        # 注意：论文中j从1开始，这里从0开始，所以使用j而不是j-1
        weight = (2**j) / normalization_factor
        
        # 计算余弦相似度
        cos_sim = F.cosine_similarity(
            index_vectors_1[j].unsqueeze(0), 
            index_vectors_2[j].unsqueeze(0), 
            dim=1
        )
        
        # 加权累加
        total_similarity += weight * cos_sim
    
    return total_similarity


def batch_hierarchical_similarity(batch_index_vectors_1, batch_index_vectors_2, num_layers):
    """
    批量计算层次化相似度
    
    Args:
        batch_index_vectors_1: [batch_size_1, num_layers, hidden_size]
        batch_index_vectors_2: [batch_size_2, num_layers, hidden_size]
        num_layers: 层次数量
        
    Returns:
        torch.Tensor: 相似度矩阵 [batch_size_1, batch_size_2]
    """
    batch_size_1 = batch_index_vectors_1.shape[0]
    batch_size_2 = batch_index_vectors_2.shape[0]
    
    # 初始化相似度矩阵
    similarity_matrix = torch.zeros(batch_size_1, batch_size_2, device=batch_index_vectors_1.device)
    
    normalization_factor = 2**num_layers - 1
    
    for j in range(num_layers):
        # 计算第j层的权重
        weight = (2**j) / normalization_factor
        
        # 计算第j层所有样本对的余弦相似度
        layer_vectors_1 = batch_index_vectors_1[:, j, :]  # [batch_size_1, hidden_size]
        layer_vectors_2 = batch_index_vectors_2[:, j, :]  # [batch_size_2, hidden_size]
        
        # 计算余弦相似度矩阵
        cos_sim_matrix = F.cosine_similarity(
            layer_vectors_1.unsqueeze(1),  # [batch_size_1, 1, hidden_size]
            layer_vectors_2.unsqueeze(0),  # [1, batch_size_2, hidden_size]
            dim=2
        )  # [batch_size_1, batch_size_2]
        
        # 加权累加
        similarity_matrix += weight * cos_sim_matrix
    
    return similarity_matrix


def retrieve_top_k_samples(query_index_vectors, database_index_vectors, database_labels, 
                          num_layers, k=3, filter_same_labels=True):
    """
    检索Top-K最相似的样本
    
    Args:
        query_index_vectors: 查询样本的索引向量 [num_layers, hidden_size]
        database_index_vectors: 数据库中所有样本的索引向量 [num_samples, num_layers, hidden_size]
        database_labels: 数据库中样本的标签 [num_samples]
        num_layers: 层次数量
        k: 检索的样本数量
        filter_same_labels: 是否过滤相同标签的样本
        
    Returns:
        dict: 包含top-k样本信息的字典
    """
    # 计算查询样本与数据库中所有样本的相似度
    query_vectors = query_index_vectors.unsqueeze(0)  # [1, num_layers, hidden_size]
    similarities = batch_hierarchical_similarity(query_vectors, database_index_vectors, num_layers)
    similarities = similarities.squeeze(0)  # [num_samples]
    
    if filter_same_labels:
        # 创建一个mask来过滤相同标签的样本
        # 这里假设我们不想检索与查询样本标签完全相同的样本
        # 在实际应用中，可能需要根据具体需求调整
        pass
    
    # 获取top-k相似的样本
    top_k_similarities, top_k_indices = torch.topk(similarities, k=min(k, len(similarities)))
    
    # 如果需要过滤相同标签，确保选择的样本标签不同
    if filter_same_labels and len(database_labels) > 0:
        selected_indices = []
        selected_similarities = []
        selected_labels = set()
        
        for sim, idx in zip(top_k_similarities, top_k_indices):
            label = database_labels[idx].item() if torch.is_tensor(database_labels[idx]) else database_labels[idx]
            if label not in selected_labels:
                selected_indices.append(idx)
                selected_similarities.append(sim)
                selected_labels.add(label)
                
                if len(selected_indices) >= k:
                    break
        
        # 如果过滤后样本不足k个，用原始结果补充
        while len(selected_indices) < k and len(selected_indices) < len(top_k_indices):
            for sim, idx in zip(top_k_similarities, top_k_indices):
                if idx not in selected_indices:
                    selected_indices.append(idx)
                    selected_similarities.append(sim)
                    if len(selected_indices) >= k:
                        break
        
        top_k_indices = torch.tensor(selected_indices, device=similarities.device)
        top_k_similarities = torch.tensor(selected_similarities, device=similarities.device)
    
    return {
        'indices': top_k_indices,
        'similarities': top_k_similarities,
        'labels': database_labels[top_k_indices] if len(database_labels) > 0 else None
    }


class HierarchicalIndexVectorExtractor:
    """
    层次化索引向量提取器
    实现论文中的索引向量提取逻辑
    """
    
    def __init__(self, model, tokenizer, num_layers):
        """
        Args:
            model: 预训练语言模型
            tokenizer: 分词器
            num_layers: 层次数量
        """
        self.model = model
        self.tokenizer = tokenizer
        self.num_layers = num_layers
        
    def extract_index_vectors(self, text, soft_prompt_embeds=None):
        """
        提取文本的层次化索引向量

        Args:
            text: 输入文本
            soft_prompt_embeds: 软提示embedding [num_layers, hidden_size]

        Returns:
            torch.Tensor: 索引向量 [num_layers, hidden_size]
        """
        # 编码文本
        inputs = self.tokenizer(text, return_tensors='pt', truncation=True, max_length=512)

        # 确保输入在正确的设备上
        device = next(self.model.parameters()).device
        inputs = {k: v.to(device) for k, v in inputs.items()}

        with torch.no_grad():
            # 如果有软提示embedding，需要将其添加到输入中
            if soft_prompt_embeds is not None:
                # 确保软提示embedding在正确的设备上
                soft_prompt_embeds = soft_prompt_embeds.to(device)

                # 获取文本的embedding
                if hasattr(self.model, 'get_input_embeddings'):
                    text_embeds = self.model.get_input_embeddings()(inputs['input_ids'])
                else:
                    text_embeds = self.model.model.embed_tokens(inputs['input_ids'])

                # 在文本前添加软提示
                batch_size = text_embeds.shape[0]
                prompt_embeds = soft_prompt_embeds.unsqueeze(0).expand(batch_size, -1, -1)

                # 拼接: [P1][P2]...[Pc]text
                full_embeds = torch.cat([prompt_embeds, text_embeds], dim=1)

                # 更新attention mask
                prompt_mask = torch.ones(batch_size, self.num_layers, device=device)
                full_attention_mask = torch.cat([prompt_mask, inputs['attention_mask']], dim=1)

                # 前向传播
                outputs = self.model(inputs_embeds=full_embeds, attention_mask=full_attention_mask,
                                   output_hidden_states=True)

                # 提取软提示位置的hidden states作为索引向量
                hidden_states = outputs.hidden_states[-1]  # 最后一层
                index_vectors = hidden_states[:, :self.num_layers, :]  # [batch_size, num_layers, hidden_size]

                return index_vectors.squeeze(0)  # [num_layers, hidden_size]
            else:
                # 没有软提示的情况，使用普通的前向传播
                outputs = self.model(**inputs, output_hidden_states=True)
                hidden_states = outputs.hidden_states[-1]

                # 简单地使用前num_layers个位置作为索引向量
                if hidden_states.shape[1] >= self.num_layers:
                    index_vectors = hidden_states[0, :self.num_layers, :]
                else:
                    # 如果序列长度不足，用平均池化
                    pooled = hidden_states.mean(dim=1)  # [hidden_size]
                    index_vectors = pooled.unsqueeze(0).expand(self.num_layers, -1)

                return index_vectors


def build_retrieval_database(texts, labels, model, tokenizer, num_layers, soft_prompt_embeds=None):
    """
    构建检索数据库
    
    Args:
        texts: 文本列表
        labels: 标签列表
        model: 预训练模型
        tokenizer: 分词器
        num_layers: 层次数量
        soft_prompt_embeds: 软提示embedding
        
    Returns:
        dict: 检索数据库
    """
    extractor = HierarchicalIndexVectorExtractor(model, tokenizer, num_layers)
    
    database_vectors = []
    database_labels = []
    
    print("Building retrieval database...")
    device = next(model.parameters()).device if hasattr(model, 'parameters') else 'cpu'

    for text, label in zip(texts, labels):
        index_vectors = extractor.extract_index_vectors(text, soft_prompt_embeds)
        # 确保向量在正确的设备上
        if torch.is_tensor(index_vectors):
            index_vectors = index_vectors.to(device)
        database_vectors.append(index_vectors)
        database_labels.append(label)
    
    database_vectors = torch.stack(database_vectors)  # [num_samples, num_layers, hidden_size]
    database_labels = torch.tensor(database_labels, device=device)
    
    return {
        'vectors': database_vectors,
        'labels': database_labels,
        'texts': texts
    }
