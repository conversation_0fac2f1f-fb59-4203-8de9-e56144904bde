#!/usr/bin/env python3
"""
实现论文中的标签描述生成功能
根据论文公式(2): d = LLM(Describe, l)
"""

import json
import torch
import os
from typing import List, Dict, Any
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False


class LabelDescriptionGenerator:
    """
    标签描述生成器
    实现论文中提到的标签描述扩展功能
    """
    
    def __init__(self, model_name_or_path: str = None, use_openai: bool = False):
        """
        Args:
            model_name_or_path: 本地LLM模型路径
            use_openai: 是否使用OpenAI API
        """
        self.use_openai = use_openai
        
        if use_openai:
            # 使用OpenAI API
            if OPENAI_AVAILABLE:
                self.client = openai.OpenAI()
            else:
                raise ImportError("OpenAI package not available")
        else:
            # 使用本地模型
            if model_name_or_path:
                self.tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_name_or_path,
                    torch_dtype=torch.bfloat16,
                    device_map="auto"
                )
            else:
                self.tokenizer = None
                self.model = None
    
    def generate_label_path_descriptions(self, processor, save_path: str = None) -> Dict[str, str]:
        """
        为所有标签路径生成描述
        
        Args:
            processor: 数据处理器，包含标签层次结构
            save_path: 保存路径
            
        Returns:
            Dict[str, str]: 标签路径到描述的映射
        """
        descriptions = {}
        
        # 获取所有叶节点标签
        leaf_labels = processor.label_list[-1]  # 最后一层是叶节点
        
        print(f"Generating descriptions for {len(leaf_labels)} leaf labels...")
        
        for leaf_idx, leaf_label in enumerate(tqdm(leaf_labels)):
            # 构建标签路径
            label_path = self._build_label_path(processor, leaf_idx)
            path_text = " -> ".join(label_path)
            
            # 生成描述
            description = self._generate_single_description(path_text)
            descriptions[path_text] = description
            
            # 也为单独的叶节点标签生成描述
            if leaf_label not in descriptions:
                single_desc = self._generate_single_description(leaf_label)
                descriptions[leaf_label] = single_desc
        
        # 保存描述
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(descriptions, f, ensure_ascii=False, indent=2)
            print(f"Label descriptions saved to {save_path}")
        
        return descriptions
    
    def _build_label_path(self, processor, leaf_label_idx: int) -> List[str]:
        """
        构建从根到叶的标签路径
        
        Args:
            processor: 数据处理器
            leaf_label_idx: 叶节点标签索引
            
        Returns:
            List[str]: 标签路径
        """
        path = []
        current_idx = leaf_label_idx
        
        # 从叶节点向上构建路径
        for level in range(len(processor.label_list) - 1, -1, -1):
            label_name = processor.label_list[level][current_idx]
            path.insert(0, label_name)
            
            # 获取父节点索引
            if level > 0:
                if hasattr(processor, 'hier_mapping') and level-1 in processor.hier_mapping:
                    parent_mapping = processor.hier_mapping[level-1][1]
                    if current_idx in parent_mapping:
                        current_idx = parent_mapping[current_idx]
                    else:
                        break
                else:
                    break
        
        return path
    
    def _generate_single_description(self, label_text: str) -> str:
        """
        为单个标签生成描述
        
        Args:
            label_text: 标签文本
            
        Returns:
            str: 生成的描述
        """
        if self.use_openai:
            return self._generate_with_openai(label_text)
        elif self.model is not None:
            return self._generate_with_local_model(label_text)
        else:
            # 如果没有可用的模型，返回简单的扩展
            return f"This is about {label_text}. It represents the concept and domain of {label_text}."
    
    def _generate_with_openai(self, label_text: str) -> str:
        """使用OpenAI API生成描述"""
        prompt = f"""Please provide a detailed description for the following hierarchical label or label path: "{label_text}"

The description should:
1. Explain what this label represents
2. Provide context about its domain or field
3. Help distinguish it from similar labels
4. Be concise but informative (2-3 sentences)

Description:"""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that generates clear, informative descriptions for hierarchical text classification labels."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"Error generating description for '{label_text}': {e}")
            return f"This label represents {label_text} in the classification hierarchy."
    
    def _generate_with_local_model(self, label_text: str) -> str:
        """使用本地模型生成描述"""
        prompt = f"""Please provide a detailed description for the hierarchical label: "{label_text}"

The description should explain what this label represents and help distinguish it from similar labels.

Description:"""
        
        try:
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            # 提取生成的部分
            generated_text = response[len(prompt):].strip()
            
            if generated_text:
                return generated_text
            else:
                return f"This label represents {label_text} in the classification hierarchy."
                
        except Exception as e:
            print(f"Error generating description for '{label_text}': {e}")
            return f"This label represents {label_text} in the classification hierarchy."


class LabelSimilarityCalculator:
    """
    计算标签相似度矩阵
    用于DCL中的难负样本选择
    """
    
    def __init__(self, model_name_or_path: str):
        """
        Args:
            model_name_or_path: 用于计算embedding的模型路径
        """
        self.tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name_or_path,
            torch_dtype=torch.bfloat16,
            device_map="auto"
        )
    
    def calculate_similarity_matrix(self, descriptions: Dict[str, str]) -> torch.Tensor:
        """
        计算标签描述的相似度矩阵
        
        Args:
            descriptions: 标签描述字典
            
        Returns:
            torch.Tensor: 相似度矩阵
        """
        labels = list(descriptions.keys())
        embeddings = []
        
        print("Computing label embeddings...")
        for label in tqdm(labels):
            description = descriptions[label]
            embedding = self._get_text_embedding(description)
            embeddings.append(embedding)
        
        # 堆叠所有embedding
        embeddings = torch.stack(embeddings)
        
        # 计算余弦相似度矩阵
        similarity_matrix = torch.cosine_similarity(
            embeddings.unsqueeze(1), 
            embeddings.unsqueeze(0), 
            dim=2
        )
        
        return similarity_matrix
    
    def _get_text_embedding(self, text: str) -> torch.Tensor:
        """
        获取文本的embedding表示
        
        Args:
            text: 输入文本
            
        Returns:
            torch.Tensor: 文本embedding
        """
        inputs = self.tokenizer(
            text, 
            return_tensors="pt", 
            truncation=True, 
            max_length=512,
            padding=True
        )
        
        with torch.no_grad():
            outputs = self.model(**inputs, output_hidden_states=True)
            # 使用最后一层的平均池化作为文本表示
            last_hidden_state = outputs.hidden_states[-1]
            # 平均池化（忽略padding token）
            attention_mask = inputs['attention_mask']
            masked_embeddings = last_hidden_state * attention_mask.unsqueeze(-1)
            summed_embeddings = masked_embeddings.sum(dim=1)
            lengths = attention_mask.sum(dim=1, keepdim=True)
            mean_embedding = summed_embeddings / lengths
            
        return mean_embedding.squeeze(0)


def generate_descriptions_for_dataset(dataset_name: str, 
                                    processor,
                                    model_path: str = None,
                                    use_openai: bool = False,
                                    save_dir: str = "./label_descriptions"):
    """
    为指定数据集生成标签描述
    
    Args:
        dataset_name: 数据集名称
        processor: 数据处理器
        model_path: 模型路径
        use_openai: 是否使用OpenAI
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成描述
    generator = LabelDescriptionGenerator(model_path, use_openai)
    descriptions_path = os.path.join(save_dir, f"{dataset_name}_descriptions.json")
    descriptions = generator.generate_label_path_descriptions(processor, descriptions_path)
    
    # 计算相似度矩阵
    if model_path:
        calculator = LabelSimilarityCalculator(model_path)
        similarity_matrix = calculator.calculate_similarity_matrix(descriptions)
        
        similarity_path = os.path.join(save_dir, f"{dataset_name}_similarity.pt")
        torch.save(similarity_matrix, similarity_path)
        print(f"Similarity matrix saved to {similarity_path}")
    
    return descriptions, similarity_matrix if model_path else None
