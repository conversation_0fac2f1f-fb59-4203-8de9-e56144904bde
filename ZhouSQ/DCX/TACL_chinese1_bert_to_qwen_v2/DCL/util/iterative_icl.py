#!/usr/bin/env python3
"""
实现论文中的迭代式In-Context Learning (ICL)提示策略
这是论文方法的核心创新之一
"""

import torch
import json
from typing import List, Dict, Any, Tuple
from transformers import AutoTokenizer, AutoModelForCausalLM
from util.similarity import retrieve_top_k_samples, hierarchical_similarity

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("Warning: OpenAI not available, will use local models only")


class IterativeICLPredictor:
    """
    迭代式ICL预测器
    实现论文中描述的逐层标签预测策略
    """
    
    def __init__(self,
                 llm_model_path: str = None,
                 use_openai: bool = False,
                 openai_model: str = "gpt-3.5-turbo",
                 temperature: float = 0.2):
        """
        Args:
            llm_model_path: 本地LLM模型路径
            use_openai: 是否使用OpenAI API
            openai_model: OpenAI模型名称
            temperature: 生成温度
        """
        self.use_openai = use_openai
        self.temperature = temperature
        
        if use_openai:
            if OPENAI_AVAILABLE:
                self.client = openai.OpenAI()
                self.model_name = openai_model
            else:
                raise ImportError("OpenAI package not available. Please install it or use local models.")
        else:
            if llm_model_path:
                self.tokenizer = AutoTokenizer.from_pretrained(llm_model_path)
                self.model = AutoModelForCausalLM.from_pretrained(
                    llm_model_path,
                    torch_dtype=torch.bfloat16,
                    device_map="auto"
                )
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
            else:
                self.tokenizer = None
                self.model = None
    
    def predict_hierarchical_labels(self, 
                                   text: str,
                                   retrieval_database: Dict,
                                   processor,
                                   index_vectors: torch.Tensor,
                                   k: int = 3) -> List[str]:
        """
        使用迭代式ICL预测层次化标签
        
        Args:
            text: 输入文本
            retrieval_database: 检索数据库
            processor: 数据处理器
            index_vectors: 输入文本的索引向量 [num_layers, hidden_size]
            k: 检索的示例数量
            
        Returns:
            List[str]: 预测的层次化标签路径
        """
        predicted_path = []
        current_parent = "Root"
        
        num_layers = len(processor.label_list)
        
        for layer in range(num_layers):
            print(f"Predicting layer {layer + 1}/{num_layers}...")
            
            # 检索相似示例
            similar_samples = self._retrieve_similar_samples(
                index_vectors, retrieval_database, processor, k, layer
            )
            
            # 构建候选标签集
            candidate_labels = self._build_candidate_labels(
                current_parent, similar_samples, processor, layer
            )
            
            if not candidate_labels:
                print(f"No candidate labels found for layer {layer}")
                break
            
            # 构建ICL提示
            prompt = self._build_icl_prompt(
                text, current_parent, candidate_labels, similar_samples, layer
            )
            
            # 使用LLM预测
            predicted_label = self._predict_with_llm(prompt, candidate_labels)
            
            if predicted_label:
                predicted_path.append(predicted_label)
                current_parent = predicted_label
                print(f"Layer {layer + 1} prediction: {predicted_label}")
            else:
                print(f"Failed to predict label for layer {layer}")
                break
        
        return predicted_path
    
    def _retrieve_similar_samples(self, 
                                 index_vectors: torch.Tensor,
                                 retrieval_database: Dict,
                                 processor,
                                 k: int,
                                 current_layer: int) -> List[Dict]:
        """
        检索相似样本
        
        Args:
            index_vectors: 查询文本的索引向量
            retrieval_database: 检索数据库
            processor: 数据处理器
            k: 检索数量
            current_layer: 当前层级
            
        Returns:
            List[Dict]: 相似样本列表
        """
        # 使用论文公式(5)计算相似度
        database_vectors = retrieval_database['vectors']  # [num_samples, num_layers, hidden_size]
        database_labels = retrieval_database['labels']
        database_texts = retrieval_database['texts']
        
        # 计算相似度
        similarities = []
        for db_vectors in database_vectors:
            sim = hierarchical_similarity(index_vectors, db_vectors, len(processor.label_list))
            similarities.append(sim)
        
        similarities = torch.tensor(similarities)
        
        # 获取top-k相似样本，确保标签不同
        top_k_similarities, top_k_indices = torch.topk(similarities, k=min(k*3, len(similarities)))
        
        # 过滤相同标签的样本
        selected_samples = []
        selected_labels = set()
        
        for sim, idx in zip(top_k_similarities, top_k_indices):
            sample_label = database_labels[idx].item()
            
            # 获取该样本在当前层的标签
            layer_label = self._get_layer_label(sample_label, processor, current_layer)
            
            if layer_label not in selected_labels:
                selected_samples.append({
                    'text': database_texts[idx],
                    'full_label': sample_label,
                    'layer_label': layer_label,
                    'similarity': sim.item(),
                    'label_path': self._get_label_path(sample_label, processor)
                })
                selected_labels.add(layer_label)
                
                if len(selected_samples) >= k:
                    break
        
        return selected_samples
    
    def _get_layer_label(self, leaf_label: int, processor, layer: int) -> str:
        """获取指定层的标签"""
        if layer == len(processor.label_list) - 1:
            # 叶节点层
            return processor.label_list[layer][leaf_label]
        else:
            # 非叶节点层，需要通过映射获取
            current_idx = leaf_label
            for l in range(len(processor.label_list) - 1, layer, -1):
                if l-1 in processor.hier_mapping and current_idx in processor.hier_mapping[l-1][1]:
                    current_idx = processor.hier_mapping[l-1][1][current_idx]
                else:
                    return "Unknown"
            
            if current_idx < len(processor.label_list[layer]):
                return processor.label_list[layer][current_idx]
            else:
                return "Unknown"
    
    def _get_label_path(self, leaf_label: int, processor) -> List[str]:
        """获取完整的标签路径"""
        path = []
        current_idx = leaf_label
        
        for layer in range(len(processor.label_list) - 1, -1, -1):
            if current_idx < len(processor.label_list[layer]):
                path.insert(0, processor.label_list[layer][current_idx])
            
            # 获取父节点索引
            if layer > 0 and layer-1 in processor.hier_mapping:
                if current_idx in processor.hier_mapping[layer-1][1]:
                    current_idx = processor.hier_mapping[layer-1][1][current_idx]
                else:
                    break
        
        return path
    
    def _build_candidate_labels(self, 
                               current_parent: str,
                               similar_samples: List[Dict],
                               processor,
                               layer: int) -> List[str]:
        """
        构建候选标签集
        实现论文中的候选标签集构建策略
        """
        candidate_labels = set()
        
        # 获取当前父标签的所有子标签
        if current_parent == "Root":
            # 根节点的子标签是第0层的所有标签
            if layer == 0:
                candidate_labels.update(processor.label_list[0])
        else:
            # 找到父标签在上一层的索引
            parent_layer = layer - 1
            if parent_layer >= 0 and current_parent in processor.label_list[parent_layer]:
                parent_idx = processor.label_list[parent_layer].index(current_parent)
                
                # 获取子标签
                if parent_layer in processor.hier_mapping:
                    child_indices = processor.hier_mapping[parent_layer][0].get(parent_idx, [])
                    for child_idx in child_indices:
                        if child_idx < len(processor.label_list[layer]):
                            candidate_labels.add(processor.label_list[layer][child_idx])
        
        # 与示例中的标签取交集
        example_labels = set()
        for sample in similar_samples:
            example_labels.add(sample['layer_label'])
        
        # 候选标签集 = 父标签的子标签 ∩ 示例标签
        final_candidates = list(candidate_labels.intersection(example_labels))
        
        # 如果交集为空，使用父标签的所有子标签
        if not final_candidates:
            final_candidates = list(candidate_labels)
        
        return final_candidates
    
    def _build_icl_prompt(self, 
                         text: str,
                         current_parent: str,
                         candidate_labels: List[str],
                         similar_samples: List[Dict],
                         layer: int) -> str:
        """
        构建ICL提示
        实现论文中的提示模板
        """
        prompt_parts = []
        
        # 任务描述
        prompt_parts.append("Your task: Select the most appropriate label from the candidate label set. Here are similar examples:")
        prompt_parts.append("")
        
        # 添加示例
        for i, sample in enumerate(similar_samples[:3]):  # 使用前3个示例
            prompt_parts.append(f"Example {i+1}:")
            prompt_parts.append(f"  Text: {sample['text']}")
            prompt_parts.append(f"  Current Label: {current_parent}")
            prompt_parts.append(f"  Candidate Label Set: {', '.join(candidate_labels)}")
            prompt_parts.append(f"  Output: {sample['layer_label']}")
            prompt_parts.append("")
        
        # 测试样本
        prompt_parts.append("Test:")
        prompt_parts.append(f"  Text: {text}")
        prompt_parts.append(f"  Current Label: {current_parent}")
        prompt_parts.append(f"  Candidate Label Set: {', '.join(candidate_labels)}")
        prompt_parts.append("  Output:")
        
        return "\n".join(prompt_parts)
    
    def _predict_with_llm(self, prompt: str, candidate_labels: List[str]) -> str:
        """
        使用LLM进行预测
        """
        if self.use_openai:
            return self._predict_with_openai(prompt, candidate_labels)
        elif self.model is not None:
            return self._predict_with_local_model(prompt, candidate_labels)
        else:
            # 如果没有可用的LLM，返回第一个候选标签
            return candidate_labels[0] if candidate_labels else ""
    
    def _predict_with_openai(self, prompt: str, candidate_labels: List[str]) -> str:
        """使用OpenAI API预测"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant for hierarchical text classification. Always respond with exactly one label from the candidate set."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=50,
                temperature=self.temperature
            )
            
            predicted_text = response.choices[0].message.content.strip()
            
            # 在候选标签中查找最匹配的
            for label in candidate_labels:
                if label.lower() in predicted_text.lower():
                    return label
            
            # 如果没有找到匹配的，返回第一个候选标签
            return candidate_labels[0] if candidate_labels else ""

        except Exception as e:
            print(f"Error with OpenAI prediction: {e}")
            return candidate_labels[0] if candidate_labels else ""
    
    def _predict_with_local_model(self, prompt: str, candidate_labels: List[str]) -> str:
        """使用本地模型预测"""
        try:
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=20,
                    temperature=self.temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            predicted_text = response[len(prompt):].strip()
            
            # 在候选标签中查找最匹配的
            for label in candidate_labels:
                if label.lower() in predicted_text.lower():
                    return label
            
            # 如果没有找到匹配的，返回第一个候选标签
            return candidate_labels[0] if candidate_labels else ""

        except Exception as e:
            print(f"Error with local model prediction: {e}")
            return candidate_labels[0] if candidate_labels else ""
