#!/usr/bin/env python3
"""
实现论文中描述的软提示模板 [P1][P2]...[Pc]x
这是论文方法的核心组件之一
"""

import torch
import torch.nn as nn
from openprompt.prompt_base import Template
from transformers import PreTrainedTokenizer
from typing import List, Dict, Any, Union
from openprompt.data_utils import InputExample


class HierarchicalSoftTemplate(Template):
    """
    实现论文中的层次化软提示模板
    格式: [P1][P2]...[Pc]x
    其中 [Pj] 是第j层的可训练软提示token
    """
    
    def __init__(self, 
                 tokenizer: PreTrainedTokenizer,
                 model,
                 num_layers: int,
                 num_tokens_per_layer: int = 1,
                 initialize_from_vocab: bool = True):
        """
        Args:
            tokenizer: 分词器
            model: 预训练语言模型
            num_layers: 层次数量 (C)
            num_tokens_per_layer: 每层的提示token数量
            initialize_from_vocab: 是否从词汇表初始化
        """
        super().__init__(tokenizer=tokenizer)
        self.model = model
        self.num_layers = num_layers
        self.num_tokens_per_layer = num_tokens_per_layer
        self.total_tokens = num_layers * num_tokens_per_layer
        
        # 获取模型的embedding维度
        if hasattr(model, 'get_input_embeddings'):
            self.hidden_size = model.get_input_embeddings().weight.shape[1]
        elif hasattr(model, 'model') and hasattr(model.model, 'embed_tokens'):
            self.hidden_size = model.model.embed_tokens.weight.shape[1]
        else:
            raise ValueError("Cannot determine model embedding size")
        
        # 创建可训练的软提示参数
        self.soft_embeds = nn.Parameter(torch.randn(self.total_tokens, self.hidden_size))
        
        if initialize_from_vocab:
            self._initialize_from_vocab()
        
        # 创建特殊token用于标识软提示位置
        self.prompt_tokens = []
        for i in range(num_layers):
            for j in range(num_tokens_per_layer):
                token = f"<P{i+1}_{j+1}>"
                self.prompt_tokens.append(token)
        
        # 添加特殊token到tokenizer（如果需要）
        if hasattr(tokenizer, 'add_special_tokens'):
            special_tokens = {"additional_special_tokens": self.prompt_tokens}
            tokenizer.add_special_tokens(special_tokens)
    
    def _initialize_from_vocab(self):
        """从词汇表随机初始化软提示embedding"""
        if hasattr(self.model, 'get_input_embeddings'):
            vocab_embeds = self.model.get_input_embeddings().weight
        elif hasattr(self.model, 'model') and hasattr(self.model.model, 'embed_tokens'):
            vocab_embeds = self.model.model.embed_tokens.weight
        else:
            return
        
        # 随机选择词汇表中的embedding进行初始化
        vocab_size = vocab_embeds.shape[0]
        random_indices = torch.randint(0, vocab_size, (self.total_tokens,))
        self.soft_embeds.data = vocab_embeds[random_indices].clone()
    
    def get_layer_embeddings(self, layer_idx: int) -> torch.Tensor:
        """获取指定层的软提示embedding"""
        start_idx = layer_idx * self.num_tokens_per_layer
        end_idx = start_idx + self.num_tokens_per_layer
        return self.soft_embeds[start_idx:end_idx]
    
    def wrap_one_example(self, example: Union[InputExample, Dict[str, Any]]) -> tuple:
        """
        包装单个样本，添加软提示模板
        实现论文中的 [P1][P2]...[Pc]x 格式

        Returns:
            tuple: (wrapped_example, others) 兼容openprompt接口
        """
        # 处理InputExample对象
        if isinstance(example, InputExample):
            text = example.text_a if hasattr(example, 'text_a') else ''

            # 创建模板文本，使用特殊token占位
            template_parts = []
            for i in range(self.num_layers):
                for j in range(self.num_tokens_per_layer):
                    template_parts.append(f"<P{i+1}_{j+1}>")

            # 添加原始文本
            template_text = " ".join(template_parts) + " " + text

            # 更新example
            example.text_a = template_text

            # 返回元组以兼容openprompt接口
            return (example, {})
        else:
            # 处理字典格式
            text = example.get('text_a', '')

            # 创建模板文本，使用特殊token占位
            template_parts = []
            for i in range(self.num_layers):
                for j in range(self.num_tokens_per_layer):
                    template_parts.append(f"<P{i+1}_{j+1}>")

            # 添加原始文本
            template_text = " ".join(template_parts) + " " + text

            # 更新example
            example['text'] = template_text
            return (example, {})
    
    def process_batch(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        处理批次数据，将软提示token替换为实际的embedding
        """
        input_ids = batch['input_ids']
        attention_mask = batch.get('attention_mask', None)
        
        # 获取模型的embedding层
        if hasattr(self.model, 'get_input_embeddings'):
            embed_layer = self.model.get_input_embeddings()
        elif hasattr(self.model, 'model') and hasattr(self.model.model, 'embed_tokens'):
            embed_layer = self.model.model.embed_tokens
        else:
            raise ValueError("Cannot find embedding layer")
        
        # 将input_ids转换为embeddings
        inputs_embeds = embed_layer(input_ids)
        
        # 替换软提示token的embedding
        batch_size = input_ids.shape[0]
        for i, token in enumerate(self.prompt_tokens):
            if hasattr(self.tokenizer, 'convert_tokens_to_ids'):
                token_id = self.tokenizer.convert_tokens_to_ids(token)
                if token_id != self.tokenizer.unk_token_id:
                    # 找到该token在序列中的位置
                    token_positions = (input_ids == token_id).nonzero(as_tuple=True)
                    if len(token_positions[0]) > 0:
                        # 替换为软提示embedding
                        soft_embed = self.soft_embeds[i].unsqueeze(0).expand(len(token_positions[0]), -1)
                        inputs_embeds[token_positions] = soft_embed
        
        # 更新batch
        batch['inputs_embeds'] = inputs_embeds
        if 'input_ids' in batch:
            del batch['input_ids']  # 使用inputs_embeds时不需要input_ids
        
        return batch
    
    def get_default_soft_token_ids(self) -> List[int]:
        """获取软提示token的ID列表"""
        token_ids = []
        for token in self.prompt_tokens:
            if hasattr(self.tokenizer, 'convert_tokens_to_ids'):
                token_id = self.tokenizer.convert_tokens_to_ids(token)
                token_ids.append(token_id)
        return token_ids
    
    def save_soft_embeds(self, path: str):
        """保存软提示embedding"""
        torch.save(self.soft_embeds.data, path)
    
    def load_soft_embeds(self, path: str):
        """加载软提示embedding"""
        self.soft_embeds.data = torch.load(path)


class SimplifiedSoftTemplate(Template):
    """
    简化版的软提示模板实现
    直接在输入序列前添加可训练的embedding
    """
    
    def __init__(self, 
                 tokenizer: PreTrainedTokenizer,
                 model,
                 num_layers: int):
        super().__init__(tokenizer=tokenizer)
        self.model = model
        self.num_layers = num_layers
        
        # 获取embedding维度
        if hasattr(model, 'get_input_embeddings'):
            self.hidden_size = model.get_input_embeddings().weight.shape[1]
        elif hasattr(model, 'model') and hasattr(model.model, 'embed_tokens'):
            self.hidden_size = model.model.embed_tokens.weight.shape[1]
        else:
            raise ValueError("Cannot determine model embedding size")
        
        # 创建软提示参数 - 每层一个token
        self.soft_embeds = nn.Parameter(torch.randn(num_layers, self.hidden_size))
        self._initialize_from_vocab()
    
    def _initialize_from_vocab(self):
        """从词汇表初始化"""
        if hasattr(self.model, 'get_input_embeddings'):
            vocab_embeds = self.model.get_input_embeddings().weight
        elif hasattr(self.model, 'model') and hasattr(self.model.model, 'embed_tokens'):
            vocab_embeds = self.model.model.embed_tokens.weight
        else:
            return
        
        vocab_size = vocab_embeds.shape[0]
        random_indices = torch.randint(0, vocab_size, (self.num_layers,))
        self.soft_embeds.data = vocab_embeds[random_indices].clone()
    
    def wrap_one_example(self, example: Dict[str, Any]) -> Dict[str, Any]:
        """简单包装，保持原有格式"""
        return example
    
    def get_soft_embeds(self) -> torch.Tensor:
        """获取软提示embedding"""
        return self.soft_embeds

    def to(self, device):
        """移动到指定设备"""
        self.soft_embeds = self.soft_embeds.to(device)
        return self
