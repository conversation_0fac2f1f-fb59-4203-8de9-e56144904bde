#!/usr/bin/env python3
"""
评估迭代式ICL的性能
实现论文中的检索式上下文学习评估
"""

import torch
import argparse
import json
import os
from tqdm import tqdm
import numpy as np

from util.utils import load_plm_from_config, print_info, set_seed
from util.iterative_icl import IterativeICLPredictor
from util.similarity import build_retrieval_database, HierarchicalIndexVectorExtractor
from models.soft_template import SimplifiedSoftTemplate
from util.eval import compute_score
from processor import PROCESSOR


def main():
    parser = argparse.ArgumentParser()
    
    # 基本参数
    parser.add_argument("--model", type=str, default='qwen3')
    parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B')
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--shot", default=5, type=int)
    parser.add_argument("--seed", default=42, type=int)
    parser.add_argument("--device", default="0", type=str)
    
    # ICL相关参数
    parser.add_argument("--use_openai", default=False, type=bool, help="Use OpenAI API for ICL")
    parser.add_argument("--openai_model", default="gpt-3.5-turbo", type=str)
    parser.add_argument("--llm_model_path", default=None, type=str, help="Path to local LLM for ICL")
    parser.add_argument("--k_examples", default=3, type=int, help="Number of examples for ICL")
    parser.add_argument("--temperature", default=0.2, type=float)
    
    # 其他参数
    parser.add_argument("--use_soft_template", default=True, type=bool)
    parser.add_argument("--max_eval_samples", default=100, type=int, help="Max samples to evaluate (for testing)")
    
    args = parser.parse_args()
    
    # 设置设备和种子
    os.environ["CUDA_VISIBLE_DEVICES"] = args.device
    set_seed(args.seed)
    use_cuda = torch.cuda.is_available()
    device = torch.device("cuda" if use_cuda else "cpu")
    
    print_info(f"Using device: {device}")
    print_info(f"Evaluating iterative ICL on {args.dataset} dataset")
    
    # 加载数据处理器
    processor = PROCESSOR[args.dataset]()
    args.depth = len(processor.label_list)
    
    print_info(f"Dataset: {args.dataset}")
    print_info(f"Depth: {args.depth}")
    print_info(f"Label counts: {[len(labels) for labels in processor.label_list]}")
    
    # 加载模型和tokenizer
    print_info("Loading PLM...")
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    
    if use_cuda:
        plm = plm.to(device)
    
    # 创建软提示模板
    if args.use_soft_template:
        template = SimplifiedSoftTemplate(
            tokenizer=tokenizer,
            model=plm,
            num_layers=args.depth
        )
        print_info("Created soft prompt template")
    else:
        template = None
    
    # 加载数据
    print_info("Loading data...")
    train_data = processor.get_train_examples(f"dataset/{args.dataset}/formatted_data", args.shot)
    test_data = processor.get_test_examples(f"dataset/{args.dataset}/formatted_data")
    
    # 限制评估样本数量（用于测试）
    if args.max_eval_samples > 0:
        test_data = test_data[:args.max_eval_samples]
        print_info(f"Limited evaluation to {len(test_data)} samples")
    
    print_info(f"Train samples: {len(train_data)}")
    print_info(f"Test samples: {len(test_data)}")
    
    # 构建检索数据库
    print_info("Building retrieval database...")
    train_texts = [example.text_a for example in train_data]
    train_labels = [example.label for example in train_data]
    
    # 获取软提示embedding（如果使用）
    soft_prompt_embeds = None
    if template and hasattr(template, 'get_soft_embeds'):
        soft_prompt_embeds = template.get_soft_embeds()
        print_info(f"Using soft prompt embeddings: {soft_prompt_embeds.shape}")
    
    # 构建检索数据库
    retrieval_database = build_retrieval_database(
        texts=train_texts,
        labels=train_labels,
        model=plm,
        tokenizer=tokenizer,
        num_layers=args.depth,
        soft_prompt_embeds=soft_prompt_embeds
    )
    
    print_info(f"Retrieval database built with {len(train_texts)} samples")
    
    # 创建ICL预测器
    print_info("Creating ICL predictor...")
    icl_predictor = IterativeICLPredictor(
        llm_model_path=args.llm_model_path or args.model_name_or_path,
        use_openai=args.use_openai,
        openai_model=args.openai_model,
        temperature=args.temperature
    )
    
    # 创建索引向量提取器
    extractor = HierarchicalIndexVectorExtractor(plm, tokenizer, args.depth)
    
    # 评估
    print_info("Starting evaluation...")
    predictions = []
    ground_truths = []
    
    for i, example in enumerate(tqdm(test_data, desc="Evaluating")):
        try:
            # 提取测试样本的索引向量
            test_index_vectors = extractor.extract_index_vectors(
                example.text_a, soft_prompt_embeds
            )
            
            # 使用迭代式ICL预测
            predicted_path = icl_predictor.predict_hierarchical_labels(
                text=example.text_a,
                retrieval_database=retrieval_database,
                processor=processor,
                index_vectors=test_index_vectors,
                k=args.k_examples
            )
            
            # 转换预测结果为标签索引
            predicted_indices = []
            for layer, pred_label in enumerate(predicted_path):
                if layer < len(processor.label_list):
                    if pred_label in processor.label_list[layer]:
                        pred_idx = processor.label_list[layer].index(pred_label)
                        predicted_indices.append(pred_idx)
                    else:
                        predicted_indices.append(0)  # 默认第一个标签
            
            # 补齐到所有层
            while len(predicted_indices) < args.depth:
                predicted_indices.append(0)
            
            predictions.append(predicted_indices)
            
            # 获取真实标签路径
            true_leaf_label = example.label
            true_path_indices = []
            current_idx = true_leaf_label
            
            for layer in range(args.depth - 1, -1, -1):
                true_path_indices.insert(0, current_idx)
                if layer > 0 and layer-1 in processor.hier_mapping:
                    if current_idx in processor.hier_mapping[layer-1][1]:
                        current_idx = processor.hier_mapping[layer-1][1][current_idx]
            
            ground_truths.append(true_path_indices)
            
            # 打印一些示例结果
            if i < 5:
                print_info(f"Example {i+1}:")
                print_info(f"  Text: {example.text_a[:100]}...")
                print_info(f"  Predicted path: {predicted_path}")
                print_info(f"  True path: {[processor.label_list[j][true_path_indices[j]] for j in range(len(true_path_indices))]}")
                print_info("")
        
        except Exception as e:
            print_info(f"Error processing example {i}: {e}")
            # 使用默认预测
            predictions.append([0] * args.depth)
            ground_truths.append([example.label] + [0] * (args.depth - 1))
    
    # 计算评估指标
    print_info("Computing evaluation metrics...")
    
    # 转换为numpy数组
    predictions = np.array(predictions)
    ground_truths = np.array(ground_truths)
    
    # 计算每层的准确率
    layer_accuracies = []
    for layer in range(args.depth):
        correct = (predictions[:, layer] == ground_truths[:, layer]).sum()
        total = len(predictions)
        accuracy = correct / total
        layer_accuracies.append(accuracy)
        print_info(f"Layer {layer + 1} accuracy: {accuracy:.4f}")
    
    # 计算整体准确率（所有层都正确）
    all_correct = np.all(predictions == ground_truths, axis=1).sum()
    overall_accuracy = all_correct / len(predictions)
    print_info(f"Overall accuracy (all layers correct): {overall_accuracy:.4f}")
    
    # 计算叶节点准确率
    leaf_accuracy = layer_accuracies[-1]
    print_info(f"Leaf node accuracy: {leaf_accuracy:.4f}")
    
    # 保存结果
    results = {
        'dataset': args.dataset,
        'shot': args.shot,
        'k_examples': args.k_examples,
        'use_openai': args.use_openai,
        'layer_accuracies': layer_accuracies,
        'overall_accuracy': overall_accuracy,
        'leaf_accuracy': leaf_accuracy,
        'total_samples': len(predictions)
    }
    
    results_path = f"results/icl_results_{args.dataset}_shot{args.shot}_k{args.k_examples}.json"
    os.makedirs("results", exist_ok=True)
    
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print_info(f"Results saved to {results_path}")
    
    # 打印最终结果
    print_info("=" * 50)
    print_info("FINAL RESULTS")
    print_info("=" * 50)
    print_info(f"Dataset: {args.dataset}")
    print_info(f"Shot: {args.shot}")
    print_info(f"K examples: {args.k_examples}")
    print_info(f"Use OpenAI: {args.use_openai}")
    print_info(f"Overall accuracy: {overall_accuracy:.4f}")
    print_info(f"Leaf accuracy: {leaf_accuracy:.4f}")
    for i, acc in enumerate(layer_accuracies):
        print_info(f"Layer {i+1} accuracy: {acc:.4f}")


if __name__ == "__main__":
    main()
