#!/usr/bin/env python3
"""
构建检索数据库
实现论文中的检索数据库构建功能
"""

import torch
import argparse
import os
import pickle
from tqdm import tqdm

from util.utils import load_plm_from_config, print_info, set_seed
from util.similarity import build_retrieval_database, HierarchicalIndexVectorExtractor
from models.soft_template import SimplifiedSoftTemplate
from models.hierVerb import HierVerbPromptForClassification
from openprompt.prompts import SoftVerbalizer
from processor import PROCESSOR


def main():
    parser = argparse.ArgumentParser()
    
    # 基本参数
    parser.add_argument("--model", type=str, default='qwen3')
    parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B')
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--shot", default=5, type=int)
    parser.add_argument("--seed", default=42, type=int)
    parser.add_argument("--device", default="0", type=str)
    
    # 其他参数
    parser.add_argument("--use_soft_template", default=True, type=bool)
    parser.add_argument("--output_dir", default="retrieval_db", type=str)
    
    args = parser.parse_args()
    
    # 设置设备和种子
    os.environ["CUDA_VISIBLE_DEVICES"] = args.device
    set_seed(args.seed)
    use_cuda = torch.cuda.is_available()
    device = torch.device("cuda" if use_cuda else "cpu")
    
    print_info(f"Using device: {device}")
    print_info(f"Building retrieval database for {args.dataset} dataset")
    
    # 加载数据处理器
    processor = PROCESSOR[args.dataset]()
    args.depth = len(processor.label_list)
    
    print_info(f"Dataset: {args.dataset}")
    print_info(f"Depth: {args.depth}")
    print_info(f"Label counts: {[len(labels) for labels in processor.label_list]}")
    
    # 加载模型和tokenizer
    print_info("Loading PLM...")
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    
    if use_cuda:
        plm = plm.to(device)
    
    # 创建软提示模板
    if args.use_soft_template:
        template = SimplifiedSoftTemplate(
            tokenizer=tokenizer,
            model=plm,
            num_layers=args.depth
        )
        print_info("Created soft prompt template")
    else:
        # 创建硬编码模板
        template_file = f"{args.dataset}_mask_template.txt"
        template_path = "template"
        text_mask = []
        for i in range(args.depth):
            text_mask.append(f'{i + 1} level: {{"mask"}}')
        text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
        
        if not os.path.exists(template_path):
            os.mkdir(template_path)
        
        template_path = os.path.join(template_path, template_file)
        if not os.path.exists(template_path):
            with open(template_path, 'w', encoding='utf-8') as fp:
                fp.write(text)
        
        from openprompt.prompts import ManualTemplate
        template = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=0)
        print_info("Created manual template")
    
    # 创建verbalizer
    verbalizer_list = []
    for i in range(args.depth):
        verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=processor.label_list[i]))
    
    # 创建模型
    print_info("Creating model...")
    model = HierVerbPromptForClassification(
        plm=plm,
        template=template,
        verbalizer_list=verbalizer_list,
        tokenizer=tokenizer,
        freeze_plm=False,
        args=args,
        processor=processor,
        plm_eval_mode=False,
        use_cuda=use_cuda
    )
    
    if use_cuda:
        model = model.to(device)
    
    # 加载数据
    print_info("Loading data...")
    train_data = processor.get_train_examples(f"dataset/{args.dataset}/formatted_data", args.shot)
    
    print_info(f"Train samples: {len(train_data)}")
    
    # 获取软提示embedding（如果使用）
    soft_prompt_embeds = None
    if args.use_soft_template and hasattr(template, 'get_soft_embeds'):
        soft_prompt_embeds = template.get_soft_embeds()
        print_info(f"Using soft prompt embeddings: {soft_prompt_embeds.shape}")
    
    # 创建索引向量提取器
    extractor = HierarchicalIndexVectorExtractor(plm, tokenizer, args.depth)
    
    # 提取索引向量
    print_info("Extracting index vectors...")
    train_texts = [example.text_a for example in train_data]
    train_labels = [example.label for example in train_data]
    
    # 构建检索数据库
    print_info("Building retrieval database...")
    retrieval_database = build_retrieval_database(
        texts=train_texts,
        labels=train_labels,
        model=plm,
        tokenizer=tokenizer,
        num_layers=args.depth,
        soft_prompt_embeds=soft_prompt_embeds
    )
    
    # 保存检索数据库
    os.makedirs(args.output_dir, exist_ok=True)
    output_path = os.path.join(args.output_dir, f"{args.dataset}_shot{args.shot}_db.pkl")
    
    with open(output_path, 'wb') as f:
        pickle.dump(retrieval_database, f)
    
    print_info(f"Retrieval database saved to {output_path}")
    print_info(f"Database contains {len(train_texts)} samples")
    print_info(f"Vector shape: {retrieval_database['vectors'].shape}")
    
    # 保存元数据
    metadata = {
        'dataset': args.dataset,
        'shot': args.shot,
        'depth': args.depth,
        'model': args.model,
        'model_path': args.model_name_or_path,
        'use_soft_template': args.use_soft_template,
        'num_samples': len(train_texts),
        'vector_shape': retrieval_database['vectors'].shape,
    }
    
    metadata_path = os.path.join(args.output_dir, f"{args.dataset}_shot{args.shot}_metadata.pkl")
    with open(metadata_path, 'wb') as f:
        pickle.dump(metadata, f)
    
    print_info(f"Metadata saved to {metadata_path}")
    print_info("Done!")


if __name__ == "__main__":
    main()
