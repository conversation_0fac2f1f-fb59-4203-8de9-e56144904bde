#!/usr/bin/env python3

import os
import torch
import sys

# 设置设备
device_id = 1
os.environ["CUDA_VISIBLE_DEVICES"] = f"{device_id}"
device = torch.device("cuda:0")

print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA device count: {torch.cuda.device_count()}")
print(f"Current device: {torch.cuda.current_device() if torch.cuda.is_available() else 'N/A'}")
print(f"Device name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A'}")

# 测试模型加载和GPU使用
try:
    # 导入必要的模块
    sys.path.append('/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL')
    from models.hierVerb import HierVerbPromptForClassification
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from openprompt.prompts import SoftVerbalizer, ManualTemplate
    
    print("\n=== Loading model ===")
    model_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B"
    
    # 加载tokenizer和模型
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    plm = AutoModelForCausalLM.from_pretrained(model_path, trust_remote_code=True, torch_dtype=torch.bfloat16)
    
    print(f"Model loaded. Parameters on device: {next(plm.parameters()).device}")
    
    # 移动模型到GPU
    print("\n=== Moving model to GPU ===")
    plm = plm.cuda()
    print(f"After cuda(): Parameters on device: {next(plm.parameters()).device}")
    
    # 创建一个简单的测试张量
    print("\n=== Testing tensor operations ===")
    test_tensor = torch.randn(2, 10).cuda()
    print(f"Test tensor device: {test_tensor.device}")
    
    # 测试模型前向传播
    print("\n=== Testing model forward pass ===")
    input_ids = torch.tensor([[1, 2, 3, 4, 5]]).cuda()
    print(f"Input tensor device: {input_ids.device}")
    
    with torch.no_grad():
        outputs = plm(input_ids)
        print(f"Output tensor device: {outputs.logits.device}")
    
    # 测试更复杂的计算来确保GPU被使用
    print("\n=== Testing intensive GPU computation ===")
    large_tensor = torch.randn(1000, 1000).cuda()
    for i in range(10):
        result = torch.matmul(large_tensor, large_tensor.T)
        print(f"Iteration {i+1}: Result shape {result.shape}, device {result.device}")

    print("\n=== GPU test completed successfully ===")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
