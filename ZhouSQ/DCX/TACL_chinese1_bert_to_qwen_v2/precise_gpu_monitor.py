#!/usr/bin/env python3
"""
精确的GPU监控脚本 - 验证训练时GPU真实使用情况
"""

import subprocess
import time
import threading
import os
import signal
import sys

class GPUMonitor:
    def __init__(self):
        self.monitoring = False
        self.gpu_data = []
    
    def start_monitoring(self, interval=0.5):
        """开始监控GPU使用情况"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=1)
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取GPU利用率和内存使用
                result = subprocess.run([
                    'nvidia-smi', 
                    '--query-gpu=timestamp,utilization.gpu,memory.used,memory.total,power.draw',
                    '--format=csv,noheader,nounits'
                ], capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for i, line in enumerate(lines):
                        if line.strip():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 5:
                                timestamp, util, mem_used, mem_total, power = parts
                                
                                data = {
                                    'timestamp': timestamp,
                                    'gpu_id': i,
                                    'utilization': int(util) if util != '[Not Supported]' else 0,
                                    'memory_used': int(mem_used),
                                    'memory_total': int(mem_total),
                                    'power': float(power) if power != '[Not Supported]' else 0
                                }
                                
                                self.gpu_data.append(data)
                                
                                # 实时显示
                                if data['utilization'] > 0 or data['memory_used'] > 100:
                                    print(f"GPU {i}: {data['utilization']}% 利用率, "
                                          f"{data['memory_used']}/{data['memory_total']}MB 内存, "
                                          f"{data['power']:.1f}W 功耗")
                
            except Exception as e:
                print(f"监控错误: {e}")
            
            time.sleep(interval)
    
    def get_summary(self):
        """获取监控总结"""
        if not self.gpu_data:
            return "没有收集到数据"
        
        summary = {}
        for data in self.gpu_data:
            gpu_id = data['gpu_id']
            if gpu_id not in summary:
                summary[gpu_id] = {
                    'max_util': 0,
                    'avg_util': 0,
                    'max_memory': 0,
                    'max_power': 0,
                    'count': 0,
                    'util_sum': 0
                }
            
            s = summary[gpu_id]
            s['max_util'] = max(s['max_util'], data['utilization'])
            s['max_memory'] = max(s['max_memory'], data['memory_used'])
            s['max_power'] = max(s['max_power'], data['power'])
            s['util_sum'] += data['utilization']
            s['count'] += 1
        
        # 计算平均值
        for gpu_id in summary:
            s = summary[gpu_id]
            s['avg_util'] = s['util_sum'] / s['count'] if s['count'] > 0 else 0
        
        return summary

def monitor_training_process(pid=None):
    """监控训练进程"""
    print("=== 精确GPU监控 ===")
    print("监控训练过程中的GPU使用情况...")
    print("按Ctrl+C停止监控\n")
    
    monitor = GPUMonitor()
    
    def signal_handler(sig, frame):
        print("\n\n=== 监控总结 ===")
        summary = monitor.get_summary()
        
        if isinstance(summary, dict):
            for gpu_id, stats in summary.items():
                print(f"GPU {gpu_id}:")
                print(f"  最大利用率: {stats['max_util']}%")
                print(f"  平均利用率: {stats['avg_util']:.1f}%")
                print(f"  最大内存使用: {stats['max_memory']} MB")
                print(f"  最大功耗: {stats['max_power']:.1f} W")
                
                if stats['max_util'] > 50:
                    print(f"  ✅ GPU {gpu_id} 被充分使用")
                elif stats['max_util'] > 10:
                    print(f"  ⚠️ GPU {gpu_id} 使用率较低")
                else:
                    print(f"  ❌ GPU {gpu_id} 几乎未使用")
        else:
            print(summary)
        
        monitor.stop_monitoring()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # 开始监控
    monitor.start_monitoring(interval=0.2)  # 每200ms采样一次
    
    print("监控已启动，现在请在另一个终端运行你的训练脚本:")
    print("cd DCL && python train_tb.py --batch_size 8")
    print("\n实时GPU状态:")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        signal_handler(None, None)

if __name__ == "__main__":
    monitor_training_process()
