#!/usr/bin/env python3
"""
GPU压力测试 - 验证GPU确实在工作
"""

import torch
import time
import threading
import subprocess

def gpu_stress_test(device_id=0, duration=30):
    """
    在指定GPU上运行压力测试
    """
    device = torch.device(f"cuda:{device_id}")
    print(f"在GPU {device_id}上运行压力测试 {duration}秒...")
    
    # 创建大型矩阵进行计算
    size = 4000  # 4000x4000矩阵
    
    start_time = time.time()
    iteration = 0
    
    while time.time() - start_time < duration:
        # 创建随机矩阵
        a = torch.randn(size, size, device=device, dtype=torch.float32)
        b = torch.randn(size, size, device=device, dtype=torch.float32)
        
        # 执行矩阵乘法（GPU密集计算）
        c = torch.matmul(a, b)
        
        # 执行一些其他操作
        d = torch.relu(c)
        e = torch.sum(d)
        
        iteration += 1
        
        if iteration % 10 == 0:
            elapsed = time.time() - start_time
            print(f"GPU {device_id}: 完成 {iteration} 次迭代, 用时 {elapsed:.1f}s")
            
            # 检查GPU内存使用
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated(device) / 1024**3
                memory_reserved = torch.cuda.memory_reserved(device) / 1024**3
                print(f"GPU {device_id} 内存: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
        
        # 清理内存
        del a, b, c, d, e
        
        # 短暂暂停避免过热
        time.sleep(0.01)
    
    print(f"GPU {device_id} 压力测试完成，总共 {iteration} 次迭代")

def monitor_nvidia_smi(duration=30):
    """
    监控nvidia-smi输出
    """
    print("开始监控nvidia-smi...")
    start_time = time.time()
    
    while time.time() - start_time < duration:
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu,memory.used,memory.total', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines):
                    if line.strip():
                        util, mem_used, mem_total = line.split(', ')
                        print(f"GPU {i}: 利用率 {util}%, 内存 {mem_used}/{mem_total} MB")
            
        except Exception as e:
            print(f"nvidia-smi监控错误: {e}")
        
        time.sleep(2)

def main():
    print("=== GPU压力测试和监控 ===\n")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，无法进行测试")
        return
    
    gpu_count = torch.cuda.device_count()
    print(f"检测到 {gpu_count} 个GPU")
    
    # 选择要测试的GPU
    test_gpu = 0
    test_duration = 30  # 30秒测试
    
    print(f"\n将在GPU {test_gpu}上运行 {test_duration}秒的压力测试")
    print("同时监控nvidia-smi输出")
    print("请在另一个终端运行: watch -n 1 nvidia-smi")
    print("\n按Enter开始测试...")
    input()
    
    # 启动nvidia-smi监控线程
    monitor_thread = threading.Thread(target=monitor_nvidia_smi, args=(test_duration,))
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # 运行GPU压力测试
    try:
        gpu_stress_test(test_gpu, test_duration)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
    
    print("\n测试完成！")
    print("如果在测试期间nvidia-smi显示GPU利用率接近100%，说明GPU工作正常")

if __name__ == "__main__":
    main()
