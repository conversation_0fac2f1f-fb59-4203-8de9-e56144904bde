#!/usr/bin/env python3
"""
测试精度修复是否有效
"""

import torch
import time
import subprocess
import threading

def monitor_gpu_usage():
    """监控GPU使用情况"""
    print("开始监控GPU使用...")
    
    for i in range(30):  # 监控30秒
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,memory.used,power.draw',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=3)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for gpu_id, line in enumerate(lines):
                    if line.strip():
                        util, mem, power = line.split(', ')
                        if int(util) > 0:
                            print(f"GPU {gpu_id}: {util}% 利用率, {mem}MB 内存, {power}W 功耗")
        except:
            pass
        
        time.sleep(1)

def test_precision_training():
    """测试精度修复后的训练"""
    print("=== 测试精度修复 ===")
    
    # 启动GPU监控
    monitor_thread = threading.Thread(target=monitor_gpu_usage)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    print("现在运行修复后的训练脚本...")
    print("命令: cd DCL && python train_tb.py --batch_size 4 --max_epochs 1")
    print("\n如果看到GPU利用率>0%，说明修复成功！")
    print("如果仍然是0%，可能需要进一步调试")

if __name__ == "__main__":
    test_precision_training()
