#!/usr/bin/env python3
"""
速度测试脚本 - 测试不同配置的训练速度
"""

import subprocess
import time
import sys

def run_speed_test(batch_size, max_steps=10):
    """运行速度测试"""
    print(f"\n=== 测试 batch_size={batch_size} ===")
    
    cmd = [
        sys.executable, "train_tb.py",
        "--batch_size", str(batch_size),
        "--max_epochs", "1",
        "--early_stop", "1"  # 早停，只测试几个batch
    ]
    
    start_time = time.time()
    
    try:
        # 运行训练，只测试前几个batch
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            cwd="DCL"
        )
        
        batch_count = 0
        batch_times = []
        
        for line in process.stdout:
            print(line.strip())
            
            # 检测batch完成
            if "Step" in line and "Multi-verb" in line:
                batch_count += 1
                if batch_count >= max_steps:
                    process.terminate()
                    break
            
            # 检测batch时间
            if "Avg batch time:" in line:
                try:
                    time_str = line.split("Avg batch time: ")[1].split("s")[0]
                    batch_time = float(time_str)
                    batch_times.append(batch_time)
                except:
                    pass
        
        process.wait()
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_batch_time = sum(batch_times) / len(batch_times) if batch_times else total_time / max(batch_count, 1)
        
        print(f"\n结果 batch_size={batch_size}:")
        print(f"  总时间: {total_time:.1f}s")
        print(f"  处理batch数: {batch_count}")
        print(f"  平均每batch: {avg_batch_time:.2f}s")
        print(f"  预计完整训练时间: {avg_batch_time * 5924 / 3600:.1f}小时")
        
        return avg_batch_time
        
    except Exception as e:
        print(f"测试失败: {e}")
        return float('inf')

def main():
    print("=== A100训练速度优化测试 ===")
    print("测试不同batch_size的训练速度...")
    
    # 测试不同的batch_size
    batch_sizes = [1, 4, 8, 16, 32]
    results = {}
    
    for batch_size in batch_sizes:
        try:
            avg_time = run_speed_test(batch_size, max_steps=5)
            results[batch_size] = avg_time
            
            # 如果某个batch_size太慢，跳过更大的
            if avg_time > 30:  # 如果超过30秒/batch，跳过更大的batch_size
                print(f"batch_size={batch_size} 太慢，跳过更大的batch_size")
                break
                
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
        except Exception as e:
            print(f"batch_size={batch_size} 测试失败: {e}")
    
    print("\n=== 速度测试总结 ===")
    best_batch_size = None
    best_time = float('inf')
    
    for batch_size, avg_time in results.items():
        print(f"batch_size={batch_size}: {avg_time:.2f}s/batch")
        if avg_time < best_time:
            best_time = avg_time
            best_batch_size = batch_size
    
    if best_batch_size:
        print(f"\n🚀 推荐配置: batch_size={best_batch_size}")
        print(f"预计训练时间: {best_time * 5924 / 3600:.1f}小时")
        
        if best_time > 5:
            print("\n⚠️ 仍然较慢，可能需要进一步优化:")
            print("1. 减少损失函数复杂度")
            print("2. 使用混合精度训练")
            print("3. 优化模型结构")
    else:
        print("\n❌ 所有配置都太慢，需要深度优化")

if __name__ == "__main__":
    main()
