#!/usr/bin/env python3
"""
测试GPU修复是否有效
"""

import os
import sys
import torch
import argparse

# 添加DCL目录到路径
sys.path.append('DCL')

def test_basic_gpu():
    """测试基础GPU功能"""
    print("=== 基础GPU测试 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        
        # 测试GPU内存分配
        try:
            device = torch.device("cuda:0")
            test_tensor = torch.randn(100, 100).to(device)
            print(f"✓ GPU内存分配成功: {test_tensor.device}")
            del test_tensor
            torch.cuda.empty_cache()
            return True
        except Exception as e:
            print(f"✗ GPU内存分配失败: {e}")
            return False
    else:
        print("✗ CUDA不可用")
        return False

def test_model_loading():
    """测试模型加载到GPU"""
    print("\n=== 模型加载测试 ===")
    
    if not torch.cuda.is_available():
        print("跳过模型测试（CUDA不可用）")
        return False
    
    try:
        from transformers import AutoTokenizer, AutoModel
        
        model_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B"
        if not os.path.exists(model_path):
            print(f"✗ 模型路径不存在: {model_path}")
            return False
        
        print("加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        print("✓ Tokenizer加载成功")
        
        print("加载模型...")
        model = AutoModel.from_pretrained(model_path, trust_remote_code=True)
        print("✓ 模型加载成功")
        
        print("移动模型到GPU...")
        device = torch.device("cuda:0")
        model = model.to(device)
        print(f"✓ 模型已移动到GPU: {next(model.parameters()).device}")
        
        # 测试前向传播
        print("测试前向传播...")
        inputs = tokenizer("这是一个测试", return_tensors="pt")
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
        print(f"✓ 前向传播成功，输出设备: {outputs.last_hidden_state.device}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_script_args():
    """测试训练脚本的参数解析"""
    print("\n=== 训练脚本参数测试 ===")
    
    try:
        # 模拟训练脚本的参数解析
        sys.argv = ['test_gpu_fix.py', '--device', '0', '--batch_size', '2', '--max_epochs', '1']
        
        from DCL.train_tb import main
        print("✓ 训练脚本导入成功")
        
        # 这里不实际运行训练，只测试参数解析
        print("注意：实际训练需要完整的数据集")
        return True
        
    except Exception as e:
        print(f"✗ 训练脚本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== GPU修复验证测试 ===\n")
    
    # 1. 基础GPU测试
    gpu_ok = test_basic_gpu()
    
    # 2. 模型加载测试
    if gpu_ok:
        model_ok = test_model_loading()
    else:
        model_ok = False
    
    # 3. 训练脚本测试
    script_ok = test_training_script_args()
    
    print("\n=== 测试总结 ===")
    print(f"基础GPU功能: {'✓' if gpu_ok else '✗'}")
    print(f"模型加载到GPU: {'✓' if model_ok else '✗'}")
    print(f"训练脚本导入: {'✓' if script_ok else '✗'}")
    
    if gpu_ok and model_ok:
        print("\n🎉 GPU环境配置正常！")
        print("建议运行训练脚本测试：")
        print("cd DCL && python train_tb.py --device 0 --batch_size 2 --max_epochs 1")
    elif gpu_ok:
        print("\n⚠️  GPU可用但模型加载有问题")
        print("请检查模型路径和依赖")
    else:
        print("\n❌ GPU环境有问题")
        print("请检查CUDA安装和GPU驱动")

if __name__ == "__main__":
    main()
