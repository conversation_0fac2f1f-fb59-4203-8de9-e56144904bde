#!/usr/bin/env python3
"""
GPU调试脚本 - 检查CUDA环境和PyTorch配置
"""

import os
import sys
import torch
import subprocess

def check_nvidia_driver():
    """检查NVIDIA驱动"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ NVIDIA驱动正常")
            print("GPU信息:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'NVIDIA-SMI' in line or 'Tesla' in line or 'GeForce' in line or 'RTX' in line or 'GTX' in line:
                    print(f"  {line.strip()}")
            return True
        else:
            print("✗ nvidia-smi 命令失败")
            print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 无法运行nvidia-smi: {e}")
        return False

def check_cuda_environment():
    """检查CUDA环境变量"""
    print("\n=== CUDA环境变量 ===")
    cuda_vars = ['CUDA_VISIBLE_DEVICES', 'CUDA_HOME', 'CUDA_PATH', 'LD_LIBRARY_PATH']
    for var in cuda_vars:
        value = os.environ.get(var, 'Not set')
        print(f"{var}: {value}")

def check_pytorch_cuda():
    """检查PyTorch CUDA配置"""
    print("\n=== PyTorch CUDA配置 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"cuDNN版本: {torch.backends.cudnn.version()}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"  内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
        
        print(f"当前设备: {torch.cuda.current_device()}")
        
        # 测试GPU内存分配
        try:
            test_tensor = torch.randn(100, 100).cuda()
            print(f"✓ GPU内存分配测试成功")
            print(f"测试张量设备: {test_tensor.device}")
            del test_tensor
            torch.cuda.empty_cache()
        except Exception as e:
            print(f"✗ GPU内存分配测试失败: {e}")
    else:
        print("✗ CUDA不可用")

def check_model_loading():
    """检查模型加载"""
    print("\n=== 模型加载测试 ===")
    model_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B"
    
    if os.path.exists(model_path):
        print(f"✓ 模型路径存在: {model_path}")
        try:
            from transformers import AutoTokenizer, AutoModel
            print("正在加载tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            print("✓ Tokenizer加载成功")
            
            print("正在加载模型...")
            model = AutoModel.from_pretrained(model_path, trust_remote_code=True)
            print("✓ 模型加载成功")
            print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
            if torch.cuda.is_available():
                print("正在将模型移动到GPU...")
                model = model.cuda()
                print(f"✓ 模型已移动到GPU: {next(model.parameters()).device}")
                
                # 测试前向传播
                print("测试前向传播...")
                inputs = tokenizer("测试文本", return_tensors="pt")
                inputs = {k: v.cuda() for k, v in inputs.items()}
                
                with torch.no_grad():
                    outputs = model(**inputs)
                print("✓ 前向传播测试成功")
                print(f"输出张量设备: {outputs.last_hidden_state.device}")
            
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"✗ 模型路径不存在: {model_path}")

def check_training_script_config():
    """检查训练脚本配置"""
    print("\n=== 训练脚本配置检查 ===")
    
    # 检查关键参数
    script_path = "DCL/train_tb.py"
    if os.path.exists(script_path):
        print(f"✓ 训练脚本存在: {script_path}")
        
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键配置
        if 'use_cuda = True' in content:
            print("✓ use_cuda设置为True")
        else:
            print("✗ use_cuda未正确设置")
            
        if 'device = torch.device("cuda:0")' in content:
            print("✓ device设置为cuda:0")
        else:
            print("? device设置可能有问题")
            
        if '.cuda()' in content:
            print("✓ 发现.cuda()调用")
        else:
            print("? 未发现.cuda()调用")
    else:
        print(f"✗ 训练脚本不存在: {script_path}")

def main():
    print("=== GPU使用问题诊断 ===\n")
    
    # 1. 检查NVIDIA驱动
    print("1. 检查NVIDIA驱动")
    nvidia_ok = check_nvidia_driver()
    
    # 2. 检查CUDA环境变量
    check_cuda_environment()
    
    # 3. 检查PyTorch CUDA配置
    check_pytorch_cuda()
    
    # 4. 检查模型加载
    if torch.cuda.is_available():
        check_model_loading()
    
    # 5. 检查训练脚本配置
    check_training_script_config()
    
    print("\n=== 诊断总结 ===")
    if not nvidia_ok:
        print("❌ 主要问题：NVIDIA驱动或GPU硬件问题")
    elif not torch.cuda.is_available():
        print("❌ 主要问题：PyTorch CUDA支持问题")
    else:
        print("✅ 基础GPU环境正常，问题可能在训练脚本配置")
        print("\n建议检查：")
        print("1. 训练脚本中的device参数设置")
        print("2. 模型和数据是否正确移动到GPU")
        print("3. 是否有代码强制使用CPU")

if __name__ == "__main__":
    main()
