#!/usr/bin/env python3
"""
A100服务器环境诊断脚本
对比3090和A100环境差异，找出问题所在
"""

import torch
import os
import subprocess
import sys
import time

def check_pytorch_installation():
    """检查PyTorch安装"""
    print("=== PyTorch安装检查 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"cuDNN版本: {torch.backends.cudnn.version()}")
    print(f"编译时CUDA版本: {torch.version.cuda}")
    
    # 检查是否是CPU版本的PyTorch
    if not torch.cuda.is_available():
        print("❌ 这是CPU版本的PyTorch！")
        return False
    
    # 检查CUDA运行时版本
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"系统CUDA版本: {result.stdout}")
        else:
            print("⚠️ nvcc命令不可用")
    except:
        print("⚠️ CUDA开发工具未安装")
    
    return True

def check_cuda_environment():
    """检查CUDA环境变量"""
    print("\n=== CUDA环境变量 ===")
    important_vars = [
        'CUDA_VISIBLE_DEVICES',
        'CUDA_HOME', 
        'CUDA_PATH',
        'LD_LIBRARY_PATH',
        'PATH'
    ]
    
    for var in important_vars:
        value = os.environ.get(var, 'Not set')
        print(f"{var}: {value}")
    
    # 检查是否有多个CUDA版本冲突
    if 'LD_LIBRARY_PATH' in os.environ:
        lib_paths = os.environ['LD_LIBRARY_PATH'].split(':')
        cuda_libs = [path for path in lib_paths if 'cuda' in path.lower()]
        if len(cuda_libs) > 1:
            print("⚠️ 检测到多个CUDA库路径，可能存在版本冲突:")
            for lib in cuda_libs:
                print(f"  {lib}")

def check_gpu_compute_capability():
    """检查GPU计算能力"""
    print("\n=== GPU计算能力检查 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过GPU检查")
        return
    
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f"GPU {i}: {props.name}")
        print(f"  计算能力: {props.major}.{props.minor}")
        print(f"  总内存: {props.total_memory / 1024**3:.1f} GB")
        print(f"  多处理器数量: {props.multi_processor_count}")
        
        # A100的计算能力应该是8.0
        if "A100" in props.name and props.major < 8:
            print(f"⚠️ A100的计算能力异常: {props.major}.{props.minor}")

def test_gpu_performance():
    """测试GPU性能"""
    print("\n=== GPU性能测试 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过性能测试")
        return
    
    device = torch.device("cuda:0")
    
    # 测试不同大小的矩阵乘法
    sizes = [1000, 2000, 4000]
    
    for size in sizes:
        print(f"\n测试 {size}x{size} 矩阵乘法:")
        
        # 预热
        a = torch.randn(size, size, device=device)
        b = torch.randn(size, size, device=device)
        torch.matmul(a, b)
        torch.cuda.synchronize()
        
        # 计时测试
        start_time = time.time()
        for _ in range(10):
            c = torch.matmul(a, b)
            torch.cuda.synchronize()
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        print(f"  平均时间: {avg_time:.4f}s")
        
        # A100应该比3090快，如果慢很多说明有问题
        if size == 4000 and avg_time > 0.1:  # 4000x4000矩阵乘法不应该超过0.1秒
            print(f"⚠️ 性能异常慢: {avg_time:.4f}s")
        
        del a, b, c

def check_conda_environment():
    """检查conda环境"""
    print("\n=== Conda环境检查 ===")
    
    # 检查当前环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'Not in conda')
    print(f"当前conda环境: {conda_env}")
    
    # 检查Python路径
    print(f"Python路径: {sys.executable}")
    
    # 检查PyTorch安装路径
    print(f"PyTorch路径: {torch.__file__}")
    
    # 检查是否有多个PyTorch安装
    try:
        result = subprocess.run([sys.executable, '-c', 
                               'import torch; print("PyTorch from:", torch.__file__)'], 
                              capture_output=True, text=True)
        print(f"PyTorch导入测试: {result.stdout.strip()}")
    except:
        print("PyTorch导入测试失败")

def check_driver_compatibility():
    """检查驱动兼容性"""
    print("\n=== 驱动兼容性检查 ===")
    
    try:
        # 获取驱动版本
        result = subprocess.run(['nvidia-smi', '--query-gpu=driver_version', 
                               '--format=csv,noheader'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            driver_version = result.stdout.strip()
            print(f"NVIDIA驱动版本: {driver_version}")
            
            # 检查驱动是否支持当前CUDA版本
            driver_major = int(driver_version.split('.')[0])
            if driver_major < 450:  # CUDA 11.0需要450+
                print(f"⚠️ 驱动版本可能过旧: {driver_version}")
        
        # 检查CUDA运行时版本
        if torch.cuda.is_available():
            runtime_version = torch.version.cuda
            print(f"CUDA运行时版本: {runtime_version}")
            
    except Exception as e:
        print(f"驱动检查失败: {e}")

def main():
    print("=== A100服务器环境诊断 ===\n")
    
    # 1. PyTorch安装检查
    pytorch_ok = check_pytorch_installation()
    
    # 2. CUDA环境检查
    check_cuda_environment()
    
    # 3. GPU计算能力检查
    check_gpu_compute_capability()
    
    # 4. 性能测试
    if pytorch_ok:
        test_gpu_performance()
    
    # 5. Conda环境检查
    check_conda_environment()
    
    # 6. 驱动兼容性检查
    check_driver_compatibility()
    
    print("\n=== 诊断总结 ===")
    print("请将以上信息与3090服务器对比，找出差异")
    print("\n常见问题:")
    print("1. PyTorch版本不匹配（CPU版本 vs GPU版本）")
    print("2. CUDA版本不兼容")
    print("3. 驱动版本问题")
    print("4. conda环境配置错误")
    print("5. 多个CUDA版本冲突")

if __name__ == "__main__":
    main()
